resource "kubernetes_namespace" "monitoring" {
  metadata {
    name = local.monitoring_namespace
  }
}

resource "kubernetes_secret" "monitoring_smoketest" {
  metadata {
    name      = "monitoring-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    grafana_host = local.grafana_dns
  }
}
resource "random_password" "grafana" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "grafana" {
  metadata {
    name      = "grafana-creds"
    namespace = local.monitoring_namespace
  }
  data = {
    admin-user     = "admin"
    admin-password = random_password.grafana.result
  }
}

resource "kubernetes_secret" "google_oauth" {
  metadata {
    name      = "google-oauth-secret"
    namespace = local.monitoring_namespace
  }
  data = {
    client_id     = local.oauth_client_id
    client_secret = local.oauth_client_secret
  }
}

# Force recreation of monitoring resources when domain changes
resource "null_resource" "domain_change_trigger" {
  triggers = {
    grafana_dns = local.grafana_dns
  }
}

# Clean up old certificate when domain changes
resource "null_resource" "cleanup_old_certificate" {
  triggers = {
    grafana_dns = local.grafana_dns
  }

  provisioner "local-exec" {
    command = <<-EOT
      # Only run cleanup if we can connect to the cluster
      if kubectl get namespace ${local.monitoring_namespace} >/dev/null 2>&1; then
        echo "Cleaning up old Grafana certificate and ingress for domain change..."
        kubectl delete certificate grafana-tls -n ${local.monitoring_namespace} --ignore-not-found=true
        kubectl delete ingress monitoring-grafana -n ${local.monitoring_namespace} --ignore-not-found=true
        echo "Cleanup completed"
      else
        echo "Cannot connect to cluster, skipping cleanup"
      fi
    EOT
  }
}

resource "helm_release" "monitoring" {
  name       = "monitoring"
  chart      = "${path.module}/vendor/monitoring/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    templatefile("${path.module}/monitoring-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/monitoring/git-ref/ref")
      grafana_dns : local.grafana_dns
      instance_name : local.name_prefix
      galoy_namespace : local.galoy_namespace
      bitcoin_namespace : local.bitcoin_namespace
      graphql_playground_url : local.graphql_playground_url
      allowed_oauth_domain : "${join(" ", local.grafana_allowed_oauth_domain)}"
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/monitoring/monitoring-scaling.yml")
  ]

  depends_on = [
    kubernetes_secret.grafana,
    null_resource.domain_change_trigger,
    null_resource.cleanup_old_certificate
  ]

  dependency_update = true

  # Force recreation when domain changes
  replace_triggered_by = [
    null_resource.domain_change_trigger
  ]
}

module "honeycomb" {
  source = "./honeycomb"

  name_prefix      = local.name_prefix
  graphql_endpoint = local.graphql_dns

  new_honeycomb_environment = local.new_honeycomb_environment
}

module "kafka_connect" {
  source = "./kafka-connect"

  name_prefix    = local.name_prefix
  kafka_sa_creds = local.kafka_sa_creds
}

terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "1.13.4"
    }

    pagerduty = {
      source  = "registry.terraform.io/pagerduty/pagerduty"
      version = "3.23.1"
    }
  }
}
